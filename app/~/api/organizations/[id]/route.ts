import { and, eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { z } from "zod";
import { withInternalAuth } from "@/core/api/middleware";
import { apiError, apiNotFound, apiSuccess } from "@/core/api/response";
import type { ApiRequest } from "@/core/api/types";
import {
  idSchema,
  validateJsonBody,
  validateParams,
} from "@/core/api/validation";
import { PERMISSIONS } from "@/core/auth/permissions";
import { getDb } from "@/core/database";
import { organizations } from "@/core/database/schema";

// Validation schemas
const updateOrganizationSchema = z.object({
  name: z.string().min(1, "Organization name is required").max(255).optional(),
  description: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  industry: z.string().optional(),
  size: z
    .enum(["startup", "small", "medium", "large", "enterprise"])
    .optional(),
});

// GET /~/api/organizations/[id] - Get organization by ID
export const GET = withInternalAuth(
  async (request: NextRequest, context: ApiRequest) => {
    try {
      // Extract ID from URL path
      const url = new URL(request.url);
      const pathSegments = url.pathname.split("/");
      const id = pathSegments[pathSegments.length - 1];

      // Validate parameters
      const paramResult = validateParams({ id }, idSchema);
      if (!paramResult.success) {
        return apiError("VALIDATION_ERROR", "Invalid parameters", 400, {
          fields: paramResult.errors,
        });
      }

      const validatedId = paramResult.data.id;
      const db = getDb();

      // Query organization with data isolation
      const [organization] = await db
        .select()
        .from(organizations)
        .where(
          and(
            eq(organizations.id, validatedId),
            eq(organizations.id, context.organizationId) // Ensure user can only access their org
          )
        )
        .limit(1);

      if (!organization) {
        return apiNotFound("Organization");
      }

      return apiSuccess(organization);
    } catch (error) {
      console.error("Error fetching organization:", error);
      return apiError("INTERNAL_ERROR", "Failed to fetch organization", 500);
    }
  },
  {
    requirePermissions: [PERMISSIONS.ORGANIZATION_READ],
    requireOrganization: true,
  }
);

// PUT /~/api/organizations/[id] - Update organization
export const PUT = withInternalAuth(
  async (request: NextRequest, context: ApiRequest) => {
    try {
      // Extract ID from URL path
      const url = new URL(request.url);
      const pathSegments = url.pathname.split("/");
      const rawId = pathSegments[pathSegments.length - 1];

      // Validate parameters
      const paramResult = validateParams({ id: rawId }, idSchema);
      if (!paramResult.success) {
        return apiError("VALIDATION_ERROR", "Invalid parameters", 400, {
          fields: paramResult.errors,
        });
      }

      // Validate request body
      const validation = await validateJsonBody(
        request,
        updateOrganizationSchema
      );
      if (!validation.success) {
        return validation.response;
      }

      const { id } = paramResult.data;
      const updateData = validation.data;
      const db = getDb();

      // Check if organization exists and user has access
      const [existingOrg] = await db
        .select()
        .from(organizations)
        .where(
          and(
            eq(organizations.id, id),
            eq(organizations.id, context.organizationId)
          )
        )
        .limit(1);

      if (!existingOrg) {
        return apiNotFound("Organization");
      }

      // Update organization
      const [updatedOrganization] = await db
        .update(organizations)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(organizations.id, id))
        .returning();

      return apiSuccess(updatedOrganization);
    } catch (error) {
      console.error("Error updating organization:", error);
      return apiError("INTERNAL_ERROR", "Failed to update organization", 500);
    }
  },
  {
    requirePermissions: [PERMISSIONS.ORGANIZATION_MANAGE],
    requireOrganization: true,
  }
);

// DELETE /~/api/organizations/[id] - Delete organization
export const DELETE = withInternalAuth(
  async (request: NextRequest, context: ApiRequest) => {
    try {
      // Extract ID from URL path
      const url = new URL(request.url);
      const pathSegments = url.pathname.split("/");
      const rawId = pathSegments[pathSegments.length - 1];

      // Validate parameters
      const paramResult = validateParams({ id: rawId }, idSchema);
      if (!paramResult.success) {
        return apiError("VALIDATION_ERROR", "Invalid parameters", 400, {
          fields: paramResult.errors,
        });
      }

      const { id } = paramResult.data;
      const db = getDb();

      // Check if organization exists and user has access
      const [existingOrg] = await db
        .select()
        .from(organizations)
        .where(
          and(
            eq(organizations.id, id),
            eq(organizations.id, context.organizationId)
          )
        )
        .limit(1);

      if (!existingOrg) {
        return apiNotFound("Organization");
      }

      // Delete organization (cascade will handle related records)
      await db.delete(organizations).where(eq(organizations.id, id));

      return apiSuccess({ message: "Organization deleted successfully" });
    } catch (error) {
      console.error("Error deleting organization:", error);
      return apiError("INTERNAL_ERROR", "Failed to delete organization", 500);
    }
  },
  {
    requirePermissions: [PERMISSIONS.ORGANIZATION_MANAGE],
    requireOrganization: true,
  }
);
